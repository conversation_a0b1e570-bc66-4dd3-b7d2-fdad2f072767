{"$message_type":"diagnostic","message":"hard linking files in the incremental compilation cache failed. copying files instead. consider moving the cache directory to a file system which supports hard linking in session dir `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working`","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: hard linking files in the incremental compilation cache failed. copying files instead. consider moving the cache directory to a file system which supports hard linking in session dir `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working`\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"function `hex_string` is never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src\\main.rs","byte_start":10175,"byte_end":10185,"line_start":253,"line_end":253,"column_start":4,"column_end":14,"is_primary":true,"text":[{"text":"fn hex_string(bytes: &[u8]) -> String {","highlight_start":4,"highlight_end":14}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: function `hex_string` is never used\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\main.rs:253:4\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m253\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mfn hex_string(bytes: &[u8]) -> String {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;11m^^^^^^^^^^\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal-d008b3d1470f666c.5c5oib9t7b0lubwnin1v39nzc.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working\\5c5oib9t7b0lubwnin1v39nzc.o`: Отказано в доступе. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal-d008b3d1470f666c.5c5oib9t7b0lubwnin1v39nzc.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working\\5c5oib9t7b0lubwnin1v39nzc.o`: Отказано в доступе. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal-d008b3d1470f666c.5lh98mdd6dxoo7wzuj4orkz9b.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working\\5lh98mdd6dxoo7wzuj4orkz9b.o`: Отказано в доступе. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error copying object file `D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\deps\\bsh_ble_terminal-d008b3d1470f666c.5lh98mdd6dxoo7wzuj4orkz9b.rcgu.o` to incremental directory as `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working\\5lh98mdd6dxoo7wzuj4orkz9b.o`: Отказано в доступе. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"error finalizing incremental compilation session directory `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working`: Отказано в доступе. (os error 5)","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: error finalizing incremental compilation session directory `\\\\?\\D:\\Dev\\Rust\\bsh_ble_terminal\\target\\debug\\incremental\\bsh_ble_terminal-2arwwluotcrre\\s-h9up2bcrlv-1869k58-working`: Отказано в доступе. (os error 5)\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"5 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;11mwarning\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: 5 warnings emitted\u001b[0m\n\n"}
