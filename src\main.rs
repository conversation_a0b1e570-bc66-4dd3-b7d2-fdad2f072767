use btleplug::api::{<PERSON>, Manager as _, Peripheral as _, <PERSON>an<PERSON><PERSON><PERSON>, WriteType};
use btleplug::platform::Manager;
use colored::*;
use futures::stream::StreamExt;
use regex::Regex;
use std::error::Error;
use std::io::{self, Write};
use std::time::Duration;
use tokio::time;
use tokio::io::{AsyncBufReadExt, BufReader};
use uuid::Uuid;

#[cfg(test)]
mod test_ansi;

// Состояния терминала
#[derive(Debug, Clone, PartialEq)]
enum TerminalState {
    ShowingMessages,  // Режим вывода логов устройства
    InputMode,        // Режим ввода команды
}

// UUID сервиса и характеристик
const SERVICE_UUID: Uuid = Uuid::from_u128(0x000000F3_0000_1000_8000_00805f9b34fb);
const READ_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F307_0000_1000_8000_00805f9b34fb);
const WRITE_CHARACTERISTIC_UUID: Uuid = Uuid::from_u128(0x0000F308_0000_1000_8000_00805f9b34fb);

// Функции для управления интерфейсом
fn show_prompt() {
    print!("\n\x1B[97m\x1B[1mДля ввода команды нажмите Enter > \x1B[0m");
    io::stdout().flush().unwrap();
}

fn clear_prompt_line() {
    print!("\r\x1B[K"); // Очищает текущую строку
    io::stdout().flush().unwrap();
}

fn show_input_prompt() {
    print!("\n> ");
    io::stdout().flush().unwrap();
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    println!("BLE Terminal - поиск устройств...");

    let manager = Manager::new().await?;
    let adapters = manager.adapters().await?;

    if adapters.is_empty() {
        eprintln!("Не найдено BLE адаптеров");
        return Ok(());
    }

    let central = &adapters[0];

    let scan_filter = ScanFilter {
        services: vec![SERVICE_UUID],
    };
    // central.start_scan(ScanFilter::default()).await?;
    central.start_scan(scan_filter).await?;

    println!("Сканирование в течение 10 секунд...");
    time::sleep(Duration::from_secs(10)).await;

    central.stop_scan().await?;

    let peripherals = central.peripherals().await?;

    if peripherals.is_empty() {
        println!("Устройства не найдены");
        return Ok(());
    }

    let mut matching_peripherals = Vec::new();

    println!("\nПоиск устройств, рекламирующих нужный сервис...");
    
    for (i, peripheral) in peripherals.iter().enumerate() {
        let properties = peripheral.properties().await?;
    
        let props = match properties {
            Some(p) => p,
            None => continue,
        };
    
        // Проверяем, рекламирует ли устройство интересующий UUID сервиса
        if let Some(uuids) = props.advertisement_data.service_uuids {
            if uuids.contains(&SERVICE_UUID) {
                let name = props
                    .local_name
                    .unwrap_or_else(|| "Неизвестное устройство".to_string());
                println!("{}. {} ({})", matching_peripherals.len() + 1, name, peripheral.address());
    
                matching_peripherals.push(peripheral.clone());
            }
        }
    }
    
    if matching_peripherals.is_empty() {
        println!("Устройства не найдены");
        return Ok(());
    } 
    
    print!("\nВыберите устройство (введите номер): ");
    io::stdout().flush()?;


    let mut input = String::new();
    io::stdin().read_line(&mut input)?;

    let choice: usize = input.trim().parse().unwrap_or(0);

    if choice == 0 || choice > peripherals.len() {
        println!("Неверный выбор");
        return Ok(());
    }

    let peripheral = &peripherals[choice - 1];
    let properties = peripheral.properties().await?;
    let device_name = properties
        .unwrap_or_default()
        .local_name
        .unwrap_or_else(|| "Неизвестное устройство".to_string());

    println!("Подключение к устройству: {}", device_name);

    peripheral.connect().await?;
    println!("Подключено!");

    peripheral.discover_services().await?;

    let services = peripheral.services();
    let service = services
        .iter()
        .find(|s| s.uuid == SERVICE_UUID)
        .ok_or("Сервис не найден")?;

    println!("Найден сервис: {}", service.uuid);

    // Поиск характеристики для чтения
    let read_characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == READ_CHARACTERISTIC_UUID)
        .ok_or("Характеристика для чтения не найдена")?;

    println!("Найдена характеристика для чтения: {}", read_characteristic.uuid);

    // Поиск характеристики для записи
    let write_characteristic = service
        .characteristics
        .iter()
        .find(|c| c.uuid == WRITE_CHARACTERISTIC_UUID)
        .ok_or("Характеристика для записи не найдена")?;

    println!("Найдена характеристика для записи: {}", write_characteristic.uuid);

    peripheral.subscribe(read_characteristic).await?;
    println!("Подписка на уведомления активирована");

    println!("\nТерминал готов!");
    println!("Нажмите Ctrl+C для выхода");
    println!("----------------------------------------");

    // потока для чтения пользовательского ввода
    let stdin = tokio::io::stdin();
    let mut reader = BufReader::new(stdin);
    let mut input_line = String::new();

    // поток уведомлений
    let mut notification_stream = peripheral.notifications().await?;

    let mut state = TerminalState::ShowingMessages;

    show_prompt();

    loop {
        match state {
            TerminalState::ShowingMessages => {
                tokio::select! {
                    // Обработка уведомлений от устройства
                    notification = notification_stream.next() => {
                        if let Some(data) = notification {
                            if data.uuid == READ_CHARACTERISTIC_UUID {
                                clear_prompt_line();
                                print_colored_string(&data.value);
                                show_prompt();
                            }
                        }
                    }
                    // Ожидание нажатия Enter для перехода в режим ввода
                    input_result = reader.read_line(&mut input_line) => {
                        match input_result {
                            Ok(0) => break, // EOF
                            Ok(_) => {
                                // переход в режим ввода
                                clear_prompt_line();
                                show_input_prompt();
                                state = TerminalState::InputMode;
                                input_line.clear();
                            }
                            Err(e) => {
                                eprintln!("Ошибка чтения ввода: {}", e);
                                break;
                            }
                        }
                    }
                }
            }
            TerminalState::InputMode => {  // режим комманд
                match reader.read_line(&mut input_line).await {
                    Ok(0) => break, // EOF
                    Ok(_) => {
                        let command = input_line.trim();
                        if !command.is_empty() {
                            // Отправка команды на устройство
                            match peripheral.write(write_characteristic, command.as_bytes(), WriteType::WithoutResponse).await {
                                Ok(_) => {
                                    println!("Отправлено: {}", command);
                                }
                                Err(e) => {
                                    eprintln!("Ошибка отправки: {}", e);
                                }
                            }
                        }
                        // Возврат в режим показа сообщений
                        state = TerminalState::ShowingMessages;
                        input_line.clear();
                        show_prompt();
                    }
                    Err(e) => {
                        eprintln!("Ошибка чтения ввода: {}", e);
                        break;
                    }
                }
            }
        }
    }

    Ok(())
}

// Функция для преобразования байтов в hex строку
fn hex_string(bytes: &[u8]) -> String {
    bytes
        .iter()
        .map(|b| format!("{:02x}", b))
        .collect::<Vec<_>>()
        .join(" ")
}



// Функция для обработки и вывода цветного текста с ANSI кодами
pub fn print_colored_string(bytes: &[u8]) {
    let text = String::from_utf8_lossy(bytes);

    // Регулярное выражение для поиска ANSI escape последовательностей
    let ansi_regex = Regex::new(r"\x1B\[([0-9;]*)m").unwrap();

    let mut last_end = 0;
    let mut current_color: Option<Color> = None;

    for mat in ansi_regex.find_iter(&text) {
        // Выводим текст до ANSI кода
        let before_ansi = &text[last_end..mat.start()];
        if !before_ansi.is_empty() {
            if let Some(color) = current_color {
                print!("{}", before_ansi.color(color));
            } else {
                print!("{}", before_ansi);
            }
        }

        // Парсинг ANSI кода
        let ansi_code = &text[mat.start()..mat.end()];
        current_color = parse_ansi_color(ansi_code);

        last_end = mat.end();
    }

    // Вывод оставшегося текста
    let remaining = &text[last_end..];
    if !remaining.is_empty() {
        if let Some(color) = current_color {
            print!("{}", remaining.color(color));
        } else {
            print!("{}", remaining);
        }
    }

    // Добавляет перенос строки, если его нет
    if !text.ends_with('\n') {
        println!();
    }
}

// Функция для парсинга ANSI цветных кодов
pub fn parse_ansi_color(ansi_code: &str) -> Option<Color> {
    let codes_str = ansi_code.trim_start_matches("\x1B[").trim_end_matches('m');

    if codes_str.is_empty() || codes_str == "0" {
        return None; // Сброс цвета
    }

    let codes: Vec<u8> = codes_str
        .split(';')
        .filter_map(|s| s.parse().ok())
        .collect();

    for &code in &codes {
        match code {
            // Стандартные цвета (30-37)
            30 => return Some(Color::Black),
            31 => return Some(Color::Red),
            32 => return Some(Color::Green),
            33 => return Some(Color::Yellow),
            34 => return Some(Color::Blue),
            35 => return Some(Color::Magenta),
            36 => return Some(Color::Cyan),
            37 => return Some(Color::White),

            // Яркие цвета (90-97)
            90 => return Some(Color::BrightBlack),
            91 => return Some(Color::BrightRed),
            92 => return Some(Color::BrightGreen),
            93 => return Some(Color::BrightYellow),
            94 => return Some(Color::BrightBlue),
            95 => return Some(Color::BrightMagenta),
            96 => return Some(Color::BrightCyan),
            97 => return Some(Color::BrightWhite),

            // Игнорируем другие коды (жирный, курсив и т.д.)
            _ => continue,
        }
    }

    None
}
